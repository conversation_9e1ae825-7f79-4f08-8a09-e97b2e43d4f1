/* 
* TDA4VH SoC
*/
#ifndef HW_ARM_TDA4VH_SOC_H
#define HW_ARM_TDA4VH_SOC_H

#include "qom/object.h"
#include "hw/sysbus.h"
#include "hw/cpu/cluster.h"
#include "hw/arm/armv7m.h"
#include "target/arm/cpu.h"
#include "qemu/units.h"
#include "qemu/log.h"
#include "hw/arm/tda4vh_mailbox.h"
#include "hw/intc/arm_gicv3.h"
#include "qobject/qlist.h"
#include "hw/char/pl011.h"
#include "chardev/char.h"
#include "monitor/monitor.h" 
#include "hw/qdev-properties.h"
#include "exec/memory.h"
#include "chardev/char-fe.h"
#include "target/arm/cpu.h"
#include "exec/address-spaces.h"
#include "system/system.h"

#define TYPE_TDA4VH_SOC "tda4vh-soc"
OBJECT_DECLARE_SIMPLE_TYPE(TDA4VHSOCState, TDA4VH_SOC)

#define TDA4VH_MAX_A72_CPUS 8U
#define TDA4VH_MAX_R5F_CPUS 8U
#define TDA4VH_MAX_C7x_CPUS 4U
#define TDA4VH_MAILBOX_CLUSTER 12U

#define TDA4VH_MAILBOX0_BASE 0x31F80000ULL
#define TDA4VH_MAILBOX1_BASE 0x31F90000ULL
#define TDA4VH_MAILBOX_SIZE 0x1000

#define TDA4VH_DDR0_LINUX_START 0x80000000
#define TDA4VH_DDR0_MAX_LINUX_SIZE 0x20000000ull /* 512 MB */

#define TDA4VH_DDR0_RTOS_START                                                 \
  (TDA4VH_DDR0_LINUX_START + TDA4VH_DDR0_MAX_LINUX_SIZE) /* 0xA0000000 */

#define TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE 0x01000000 /* 16 MB */

#define TDA4VH_DDR0_MAX_RTOS_SIZE                                              \
  (TDA4VH_DDR0_RTOS_CORE_TOTAL_SIZE *                                          \
   (TDA4VH_MAX_R5F_CPUS + TDA4VH_MAX_C7x_CPUS)) /* 192 MB */

#define TDA4VH_DDR0_SHARED_SPACE_START                                         \
  (TDA4VH_DDR0_RTOS_START + TDA4VH_DDR0_MAX_RTOS_SIZE) /*0xAC000000*/

#define TDA4VH_DDR0_SHARED_SPACE_SIZE 0x03000000 /* 48 MB */

#define TDA4VH_DDR0_MIN_SIZE                                                   \
  (TDA4VH_DDR0_MAX_LINUX_SIZE + TDA4VH_DDR0_MAX_RTOS_SIZE +                    \
   TDA4VH_DDR0_SHARED_SPACE_SIZE) /* 752 MB */

#define TDA4VH_NUM_A72_CPUS 1U
#define TDA4VH_NUM_R5F_CPUS 1U
#define TDA4VH_NUM_C7x_DSPS 1U

#define TDA4VH_DDR0_MAX_SIZE (32ull * GiB)

#define TDA4VH_A72_CLUSTER_ID 0U
#define TDA4VH_R5F_CLUSTER_ID 1U
#define TDA4VH_R5F_RAT_NUM 5U
#define TDA4VH_R5F_NON_RAT_NUM 4U

/* GICv3 register definition*/
#define TDA4VH_GIC_APU_DISTRIBUTION_BASE 0x1800000U
#define TDA4VH_GIC_APU_DISTRIBUTION_SIZE 0x10000
/* TODO: each APU will have a redistributor, 
 * the rest will be addED later*/
#define TDA4VH_GIC_APU_REDISTRIBUTOR_0_BASE 0x1900000U
#define TDA4VH_GIC_APU_REDISTRIBUTOR_0_SIZE 0x20000
#define TDA4VH_GIC_SPI_INTR_NUM 256

/* ARM Generic Timer IRQ definitions (PPIs) */
#define ARCH_TIMER_S_EL1_IRQ     13
#define ARCH_TIMER_NS_EL1_IRQ    14
#define ARCH_TIMER_VIRT_IRQ      11
#define ARCH_TIMER_NS_EL2_IRQ    10
#define ARCH_GIC_MAINT_IRQ       9

/* IRQ mapping for J784S4 devices */
enum {
    J784S4_UART0_IRQ = 32,   /* First SPI interrupt */
    J784S4_UART1_IRQ = 33,
    J784S4_UART2_IRQ = 34,
    J784S4_UART3_IRQ = 35,
    J784S4_UART4_IRQ = 36,
    J784S4_UART5_IRQ = 37,
    J784S4_UART6_IRQ = 38,
    J784S4_UART7_IRQ = 39,
    J784S4_UART8_IRQ = 40,
    J784S4_UART9_IRQ = 41,
    J784S4_MAILBOX_IRQ = 42, /* Mailbox interrupt */
    J784S4_TIMER_IRQ = 43,   /* Timer interrupt */
    J784S4_GPIO_IRQ = 44,    /* GPIO interrupt */
    /* Add more device IRQs as needed */
};

/* TDA4VH UART base addresses (from TDA4VH documentation) */
static const hwaddr j784s4_uart_addrs[] = {
    0x02800000, /* UART0 */
    0x02810000, /* UART1 */
    0x02820000, /* UART2 */
    0x02830000, /* UART3 */
    0x02840000, /* UART4 */
    0x02850000, /* UART5 */
    0x02860000, /* UART6 */
    0x02870000, /* UART7 */
    0x02880000, /* UART8 */
    0x02890000, /* UART9 */
};

/* R5F CPU view*/
#define TDA4VH_R5F_PROCESSOR_VIEW_MEMORY_SIZE 0x100000000 /* 4GB */
#define TDA4VH_R5F_ATCM_SIZE 0x8000                       /* 32MB */
#define TDA4VH_R5F_BTCM_SIZE 0x8000
#define TDA4VH_R5F_ATCM_BASE 0x0
#define TDA4VH_R5F_BTCM_BASE 0x41010000
#define TDA4VH_STUB_MEMORY_BASE 0x40f80004 
#define TDA4VH_STUB_MEMORY_SIZE 0x8fffc
enum {
  NON_RAT_SOC_0,
  NON_RAT_SOC_1,
  NON_RAT_SOC_2,
  NON_RAT_SOC_3,
};

enum {
  RAT_REGION_0,
  RAT_REGION_1,
  RAT_REGION_2,
  RAT_REGION_3,
  RAT_REGION_4,
};


struct TDA4VHSOCState {
  /*< private >*/
  DeviceState parent_obj;

  /*< public >*/
  CPUClusterState a72_cluster;
  CPUClusterState r5f_cluster;
  ARMCPU a72_cpus[TDA4VH_NUM_A72_CPUS];
  ARMCPU r5f_cpus[TDA4VH_NUM_R5F_CPUS];
  /* TODO: add ISS for C71x in future*/
  // ARMCPU c7x_dsps[TDA4VH_NUM_C7x_DSPS];

  MemoryRegion rpu_memory[TDA4VH_NUM_R5F_CPUS];
  MemoryRegion ddr_ram;
  TDA4MailBoxState MailBox0[TDA4VH_MAILBOX_CLUSTER];
  TDA4MailBoxState MailBox1[TDA4VH_MAILBOX_CLUSTER];
  PL011State uart[ARRAY_SIZE(j784s4_uart_addrs)];
  GICv3State gic;

  MemoryRegion stubMemory;

  /* R5F CPU view*/
  MemoryRegion ATCM[TDA4VH_NUM_R5F_CPUS];
  MemoryRegion BTCM[TDA4VH_NUM_R5F_CPUS];
  MemoryRegion NON_RAT_SOC_REGION[TDA4VH_NUM_R5F_CPUS][TDA4VH_R5F_NON_RAT_NUM];
  MemoryRegion RAT_REGION[TDA4VH_NUM_R5F_CPUS][TDA4VH_R5F_RAT_NUM];
};

#endif /* HW_ARM_TDA4VH_SOC_H */
