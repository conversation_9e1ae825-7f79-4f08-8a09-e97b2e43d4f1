#ifndef TDA4_IPC_H
#define TDA4_IPC_H

#include "qom/object.h"
#include "hw/sysbus.h"
#include "qemu/fifo32.h"

#define NUMS_REGSx_MESSAGE 16
#define NUMS_REGSx_FIFO_STATUS 16
#define NUMS_REGSx_MSG_STATUS 16
#define NUMS_REGSx_USERS_IRQ_STATUS_RAW 4
#define NUMS_REGSx_USERS_IRQ_STATUS_CLR 4
#define NUMS_REGSx_USERS_IRQ_ENABLE_SET 4
#define NUMS_REGSx_USERS_IRQ_ENABLE_CLR 4
#define REGSx_RESET_VALUE 0x66FCA100
#define REGSx_INTERRUPT_RESET_VALUE 0xAAAAAAAA

#define TYPE_TDA4_MAILBOX "tda4-mailbox"
OBJECT_DECLARE_SIMPLE_TYPE(TDA4MailBoxState, TDA4_MAILBOX)

struct TDA4MailBoxState {
  SysBusDevice parent_obj;

  MemoryRegion mmio;

  /*Register list*/
  uint32_t REGSx_REVISION;
  uint32_t REGSx_SYSCONFIG;
  uint32_t REGSx_MESSAGE[NUMS_REGSx_MESSAGE];
  uint32_t REGSx_FIFO_STATUS[NUMS_REGSx_FIFO_STATUS];
  uint32_t REGSx_MSG_STATUS[NUMS_REGSx_MSG_STATUS];
  uint32_t REGSx_IRQ_EOI;
  uint32_t REGSx_USERS_IRQ_STATUS_RAW[NUMS_REGSx_USERS_IRQ_STATUS_RAW];
  uint32_t REGSx_USERS_IRQ_STATUS_CLR[NUMS_REGSx_USERS_IRQ_STATUS_CLR];
  uint32_t REGSx_USERS_IRQ_ENABLE_SET[NUMS_REGSx_USERS_IRQ_ENABLE_SET];
  uint32_t REGSx_USERS_IRQ_ENABLE_CLR[NUMS_REGSx_USERS_IRQ_ENABLE_CLR];

  /*MAILBOX_MESSAGE*/
  Fifo32 MAILBOX_MESSAGE[NUMS_REGSx_MESSAGE];
};

#endif
