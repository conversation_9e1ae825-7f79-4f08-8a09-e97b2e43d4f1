/*
 * TI J784S4 Evaluation Module
*/
#include "qemu/osdep.h"
#include "qemu/error-report.h"
#include "hw/boards.h"
#include "qom/object.h"
#include "hw/arm/boot.h"
#include "hw/arm/tda4vh_soc.h"
#include "hw/sysbus.h"
#include "qapi/error.h"

struct J784S4EVMState {
  MachineState parent_obj;

  TDA4VHSOCState soc;

  struct arm_boot_info binfo;
};

/**
 * when QEMU it call machine_class_base_init to determine machine's name
 * have to mapp this type to MACHINE_TYPE_NAME to correct that process.
 */
#define TYPE_J784S4_EVM MACHINE_TYPE_NAME("j784s4-evm")
OBJECT_DECLARE_SIMPLE_TYPE(J784S4EVMState, J784S4_EVM)

static void j784s4_evm_init(MachineState *machine) {
  J784S4EVMState *s = J784S4_EVM(machine);
  uint64_t ram_size = machine->ram_size;

  /* Create the memory region to pass to the SoC */
  if (ram_size > TDA4VH_DDR0_MAX_SIZE) {
    error_report("ERROR: RAM size 0x%" PRIx64 " above max supported of "
                 "0x%llx",
                 ram_size, TDA4VH_DDR0_MAX_SIZE);
    exit(1);
  }

  if (ram_size < TDA4VH_DDR0_MIN_SIZE) {
    qemu_log("WARNING: RAM size 0x%" PRIx64 " is small for TDA4VH", ram_size);
    exit(1);
  }
  qemu_log("INFO: RAM size user input 0x%" PRIx64 " ", ram_size);

  /* Initialize TDA4VH SoC */
  object_initialize_child(OBJECT(machine), "soc", &s->soc, TYPE_TDA4VH_SOC);

  /* Realize the device */
  qdev_realize(DEVICE(&s->soc), NULL, &error_fatal);

  /* Set up boot info */
  s->binfo.ram_size = ram_size;
  s->binfo.loader_start = TDA4VH_DDR0_LINUX_START;
  s->binfo.psci_conduit = 2; /* QEMU_PSCI_CONDUIT_HVC */

  /* Load kernel if specified */
  arm_load_kernel(&s->soc.a72_cpus[0], machine, &s->binfo);
}

static void j784s4_evm_class_init(ObjectClass *oc, void *data) {
  (void)data;
  MachineClass *mc = MACHINE_CLASS(oc);

  mc->desc = "TI J784S4 Evalution Module with TDA4VH SoC";
  mc->max_cpus = TDA4VH_NUM_A72_CPUS + TDA4VH_NUM_R5F_CPUS;
  mc->default_ram_size = TDA4VH_DDR0_MIN_SIZE;
  mc->auto_create_sdcard = false;
  mc->default_cpus = TDA4VH_NUM_A72_CPUS + TDA4VH_NUM_R5F_CPUS;
  mc->min_cpus = TDA4VH_NUM_A72_CPUS + TDA4VH_NUM_R5F_CPUS;

  mc->init = j784s4_evm_init;
}

static const TypeInfo j784s4_evm_type_info = {
    .name = TYPE_J784S4_EVM,
    .parent = TYPE_MACHINE,
    .class_init = j784s4_evm_class_init,
    .instance_size = sizeof(J784S4EVMState),
};

static void j784s4_evm_register_types(void) {
  type_register_static(&j784s4_evm_type_info);
}

type_init(j784s4_evm_register_types)
