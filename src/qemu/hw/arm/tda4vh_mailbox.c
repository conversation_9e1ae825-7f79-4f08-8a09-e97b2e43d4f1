#include "qemu/osdep.h"
#include "qemu/module.h"
#include "hw/qdev-properties.h"
#include "qom/object.h"
#include "hw/sysbus.h"
#include "hw/arm/tda4vh_mailbox.h"

static void tda4_ipc_reset(DeviceState *dev) {
  TDA4MailBoxState *s = TDA4_MAILBOX(dev);
  /* Reset all registers to their default values */
  s->REGSx_REVISION = REGSx_RESET_VALUE;
  s->REGSx_SYSCONFIG = 0x0;
  memset(s->REGSx_MESSAGE, 0, sizeof(s->REGSx_MESSAGE[0]) * NUMS_REGSx_MESSAGE);
  memset(s->REGSx_FIFO_STATUS, 0,
         sizeof(s->REGSx_FIFO_STATUS[0]) * NUMS_REGSx_FIFO_STATUS);
  memset(s->REGSx_MSG_STATUS, 0,
         sizeof(s->REGSx_MSG_STATUS[0]) * NUMS_REGSx_MSG_STATUS);
  s->REGSx_IRQ_EOI = 0x0;
  memset(s->REGSx_USERS_IRQ_STATUS_RAW, REGSx_INTERRUPT_RESET_VALUE,
         sizeof(s->REGSx_USERS_IRQ_STATUS_RAW[0]) *
             NUMS_REGSx_USERS_IRQ_STATUS_RAW);
  memset(s->REGSx_USERS_IRQ_STATUS_CLR, 0,
         sizeof(s->REGSx_USERS_IRQ_STATUS_CLR[0]) *
             NUMS_REGSx_USERS_IRQ_STATUS_CLR);
  memset(s->REGSx_USERS_IRQ_ENABLE_SET, 0,
         sizeof(s->REGSx_USERS_IRQ_ENABLE_SET[0]) *
             NUMS_REGSx_USERS_IRQ_ENABLE_SET);
  memset(s->REGSx_USERS_IRQ_ENABLE_CLR, 0,
         sizeof(s->REGSx_USERS_IRQ_ENABLE_CLR[0]) *
             NUMS_REGSx_USERS_IRQ_ENABLE_CLR);

  /* Reset all FIFOs */
  for (int i = 0; i < NUMS_REGSx_MESSAGE; i++) {
    fifo32_reset(&s->MAILBOX_MESSAGE[i]);
  }
}

static uint64_t tda4_mailbox_read(void *opaque, hwaddr offset, unsigned size) {
  TDA4MailBoxState *s = (TDA4MailBoxState *)opaque;
  switch (offset) {
  case 0x0: /*REGSx_REVISION*/ 
    return s->REGSx_REVISION;
  case 0x10: /*REGSx_SYSCONFIG*/ 
    return s->REGSx_SYSCONFIG;
  case 0x40 ... 0x7C: /*pop QUEUE message when reading*/ 
    if (fifo32_is_empty(&s->MAILBOX_MESSAGE[(offset - 0x40) / 0x4])) {
      return 0;
    }
    uint32_t value = fifo32_pop(&s->MAILBOX_MESSAGE[(offset - 0x40) / 0x4]);
    s->REGSx_MESSAGE[(offset - 0x40) / 0x4] = value;
    return value;
  case 0x80 ... 0xBC: /*REGSx_FIFO_STATUS_j(full or not)*/ 
    bool is_full = fifo32_is_full(&s->MAILBOX_MESSAGE[(offset - 0x80) / 0x4]);
    return (uint32_t)is_full;
  case 0xC0 ... 0xFC: /* Number of messages in the FIFO */ 
    uint32_t num_used =
        fifo32_num_used(&s->MAILBOX_MESSAGE[(offset - 0xC0) / 0x4]);
    s->REGSx_MSG_STATUS[(offset - 0xC0) / 0x4] = num_used;
    return num_used;
  /* TODO: Register related to interrupt are not implemented yet*/
  case 0x100 ... 0x13C:
    if ((offset - 0x100) % 0x10 == 0) {
      return s->REGSx_USERS_IRQ_STATUS_RAW[(offset - 0x100) / 0x10];
    } else if ((offset - 0x104) % 0x10 == 0) {
      return s->REGSx_USERS_IRQ_STATUS_CLR[(offset - 0x104) / 0x10];
    } else if ((offset - 0x108) % 0x10 == 0) {
      return s->REGSx_USERS_IRQ_ENABLE_SET[(offset - 0x108) / 0x10];
    } else if ((offset - 0x10C) % 0x10 == 0) {
      return s->REGSx_USERS_IRQ_ENABLE_CLR[(offset - 0x10C) / 0x10];
    } else {
      return 0;
    }
  case 0x140:
    return s->REGSx_IRQ_EOI;
  default:
    /* Dummy value no need macro for it*/
    return 0x12345678;
  }
}

static void tda4_mailbox_write(void *opaque, hwaddr offset, uint64_t value64,
                           unsigned size) {
  TDA4MailBoxState *s = (TDA4MailBoxState *)opaque;
  uint32_t value = value64;
  switch (offset) {
  case 0x0: /*REGSx_REVISION*/ 
    /*Read only register*/
    break;
  case 0x10: /*REGSx_SYSCONFIG*/ 
    if (value & 0x1) {
      tda4_ipc_reset(DEVICE(s));
    }
    break;
  case 0x40 ... 0x7C: /*REGSx_MESSAGE_j*/
    fifo32_push(&s->MAILBOX_MESSAGE[(offset - 0x40) / 0x4], value);
    break;
  case 0x80 ... 0xBC: /*REGSx_FIFO_STATUS_j*/ 
    /*Read only register*/
    break;
  case 0xC0 ... 0xFC: /*REGSx_MSG_STATUS_j*/ 
    /*Read only register*/
    break;
  /* TODO: Register related to interrupt are not implemented*/
  case 0x100 ... 0x13C:
    if ((offset - 0x100) % 0x10 == 0) /*REGSx_USERS_IRQ_STATUS_RAW_j*/
    {
      s->REGSx_USERS_IRQ_STATUS_RAW[(offset - 0x100) / 0x10] = value;
      break;
    } else if ((offset - 0x104) % 0x10 == 0) /*REGSx_USERS_IRQ_STATUS_CLR_j*/
    {
      s->REGSx_USERS_IRQ_STATUS_CLR[(offset - 0x104) / 0x10] = value;
      break;
    } else if ((offset - 0x108) % 0x10 == 0) /*REGSx_USERS_IRQ_ENABLE_SET_j*/
    {
      s->REGSx_USERS_IRQ_ENABLE_SET[(offset - 0x108) / 0x10] = value;
      break;
    } else if ((offset - 0x10C) % 0x10 == 0) /*REGSx_USERS_IRQ_ENABLE_CLR_j*/
    {
      s->REGSx_USERS_IRQ_ENABLE_CLR[(offset - 0x10C) / 0x10] = value;
      break;
    } else {
      break;
    }
  case 0x140: /*REGSx_IRQ_EOI*/
    s->REGSx_IRQ_EOI = value & 0xF;
    break;
  default:
    break;
  }
}
static const MemoryRegionOps tda4_ipc_ops = {
    .read = tda4_mailbox_read,
    .write = tda4_mailbox_write,
    .endianness = DEVICE_NATIVE_ENDIAN,
    .valid.min_access_size = 4,
    .valid.max_access_size = 4,
};

static void tda4_mailbox_realize(DeviceState *dev, Error **errp) {
  TDA4MailBoxState *s = TDA4_MAILBOX(dev);
  memory_region_init_io(&s->mmio, OBJECT(dev), &tda4_ipc_ops, s,
                        TYPE_TDA4_MAILBOX, 0x200);
  sysbus_init_mmio(SYS_BUS_DEVICE(dev), &s->mmio);
  tda4_ipc_reset(dev);
  for (int i = 0; i < NUMS_REGSx_MESSAGE; i++) {
    fifo32_create(&s->MAILBOX_MESSAGE[i], 4); // Queue depth of 4
  }
}

static void tda4_mailbox_class_init(ObjectClass *klass, void *data) {
  DeviceClass *dc = DEVICE_CLASS(klass);
  dc->realize = tda4_mailbox_realize;
  dc->user_creatable =
      false; // can't create the device directly, such as command line
}

static const TypeInfo tda4_mailbox_info = {
    .name = TYPE_TDA4_MAILBOX,
    .parent = TYPE_SYS_BUS_DEVICE,
    .instance_size = sizeof(TDA4MailBoxState),
    .class_init = tda4_mailbox_class_init,
};

static void tda4_mailbox_register_types(void) {
  type_register_static(&tda4_mailbox_info);
}

type_init(tda4_mailbox_register_types)
