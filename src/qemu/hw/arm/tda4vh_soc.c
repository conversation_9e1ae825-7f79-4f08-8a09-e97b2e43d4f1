/*
* TDA4VH SoC
*/

#include "qemu/osdep.h"
#include "hw/boards.h"
#include "qom/object.h"
#include "hw/arm/boot.h"
#include "hw/arm/tda4vh_soc.h"
#include "hw/sysbus.h"
#include "hw/irq.h"
#include "hw/misc/unimp.h"
#include "qemu/error-report.h"
#include "exec/address-spaces.h"
#include <stdio.h>

const MemMapEntry NonRatMemory[] = {
  [NON_RAT_SOC_0] = {0x2000000, 0x8000},
  [NON_RAT_SOC_1] = {0xf000000, 0xf80000},
  [NON_RAT_SOC_2] = {0xff84000, 0xc000},
  [NON_RAT_SOC_3] = {0xff91000, 0x6f000},
};

const MemMapEntry RatMemory[] = {
  [RAT_REGION_0] = {0x8000, 0x1ff8000},
  [RAT_REGION_1] = {0x4000000, 0xf80000},
  [RAT_REGION_2] = {0x10000000, 0x31010000},
  [RAT_REGION_3] = {0x41018000, 0x3efe8000},
  [RAT_REGION_4] = {0x80000000, 0x80000000},
};
static void tda4vh_create_r5f_cpus(DeviceState *dev_soc, MachineState *ms,
                                  Error **errp) {
  TDA4VHSOCState *s = TDA4VH_SOC(dev_soc);
  int num_rpus =
      MIN((int)(ms->smp.cpus - TDA4VH_NUM_R5F_CPUS), TDA4VH_NUM_R5F_CPUS);

  if (num_rpus <= 0) {
    /* Don't create r5f_cluster object if there's nothing to put in it */
    qemu_log("WARNING: No R5F CPUs to create, returning\n");
    return;
  }

  object_initialize_child(OBJECT(s), "r5f_cluster", &s->r5f_cluster,
                          TYPE_CPU_CLUSTER);
  qdev_prop_set_uint32(DEVICE(&s->r5f_cluster), "cluster-id",
                       TDA4VH_R5F_CLUSTER_ID);

  for (uint8_t i = 0; i < num_rpus; i++) {
    g_autofree char *rpu_mem_name = g_strdup_printf("rpu-%d-memory", i);

    object_initialize_child(OBJECT(&s->r5f_cluster), "r5f_cpus[*]",
                            &s->r5f_cpus[i], ARM_CPU_TYPE_NAME("cortex-r5f"));

    /* TODO: temporary set power all to all core exist in SoC, improve in
     * future*/
    // qemu_log("DEBUG: Setting R5F CPU %d to start powered-off\n", i);
    // object_property_set_bool(OBJECT(&s->r5f_cpus[i]),
    //                             "start-powered-off", true, &error_abort);

    /* Create R5F 4GB processor view*/
    memory_region_init(&s->rpu_memory[i], OBJECT(ms), rpu_mem_name,
                       TDA4VH_R5F_PROCESSOR_VIEW_MEMORY_SIZE);
    object_property_set_link(OBJECT(&s->r5f_cpus[i]), "memory",
                             OBJECT(&s->rpu_memory[i]), &error_abort);

    /* R5F boot region set default to boot with ATCM*/
    memory_region_init_ram(&s->ATCM[i], OBJECT(dev_soc), "ATCM",
                           TDA4VH_R5F_ATCM_SIZE, &error_abort);
    memory_region_add_subregion_overlap(&s->rpu_memory[i], TDA4VH_R5F_ATCM_BASE, &s->ATCM[i], 1);
    memory_region_init_ram(&s->BTCM[i], OBJECT(dev_soc), "BTCM",
                           TDA4VH_R5F_BTCM_SIZE, &error_abort);
    memory_region_add_subregion_overlap(&s->rpu_memory[i], TDA4VH_R5F_BTCM_BASE,
                                        &s->BTCM[i], 1);

    /* NON_RAT_SOC_REGION directly map to SoC view*/
    for (int j = 0;j < TDA4VH_R5F_NON_RAT_NUM; j++)
    {
      g_autofree char *non_rat_name = g_strdup_printf("NON_RAT_REGION%d", j);
      memory_region_init_alias(&s->NON_RAT_SOC_REGION[i][j], OBJECT(dev_soc),
                             non_rat_name, get_system_memory(),
                             NonRatMemory[j].base,
                             NonRatMemory[j].size);
      memory_region_add_subregion_overlap(&s->rpu_memory[i],
                                        NonRatMemory[j].base,
                                        &s->NON_RAT_SOC_REGION[i][j], 1);
    }

    for (int j = 0; j < TDA4VH_R5F_RAT_NUM ; j++)
    {
      g_autofree char *rat_name = g_strdup_printf("RAT_REGION%d", j);
      memory_region_init_alias(&s->RAT_REGION[i][j], OBJECT(dev_soc),
                             rat_name, get_system_memory(),
                             RatMemory[j].base,
                             RatMemory[j].size);
      memory_region_add_subregion_overlap(
        &s->rpu_memory[i], RatMemory[j].base, &s->RAT_REGION[i][j], 0);
    }

    /* TODO: temporary unset R5F as reset hight vector, improve in future*/
    // qemu_log("DEBUG: Setting R5F CPU %d reset-hivecs\n", i);
    // object_property_set_bool(OBJECT(&s->r5f_cpus[i]), "reset-hivecs", true,
    //                          &error_abort);

    if (!qdev_realize(DEVICE(&s->r5f_cpus[i]), NULL, errp)) {
      error_report("ERROR: Failed to realize R5F CPU %d\n", i);
    }
  }

  qdev_realize(DEVICE(&s->r5f_cluster), NULL, &error_fatal);
}

static void tda4vh_create_a72_cpus(MachineState *ms, TDA4VHSOCState *s,
                                   Error **errp) {

  object_initialize_child(OBJECT(s), "a72_cluster", &s->a72_cluster,
                          TYPE_CPU_CLUSTER);
  qdev_prop_set_uint32(DEVICE(&s->a72_cluster), "cluster-id",
                       TDA4VH_A72_CLUSTER_ID);
  int num_apus = MIN(ms->smp.cpus, TDA4VH_NUM_A72_CPUS);

  for (int i = 0; i < num_apus; i++) {
    Object *obj;
    object_initialize_child(OBJECT(&s->a72_cluster), "a72_cpus[*]",
                            &s->a72_cpus[i], ARM_CPU_TYPE_NAME("cortex-a72"));
    obj = OBJECT(&s->a72_cpus[i]);

    /* Disable TrustZone/EL3 */
    object_property_set_bool(obj, "has_el3", false, &error_abort);

    /* TODO: temporary set power all to all core exist in SoC, improve in
     * future*/
    // if (i) {
    //     /* Secondary CPUs start in powered-down state */
    //     object_property_set_bool(obj, "start-powered-off", true,
    //                              &error_abort);
    // }

    if (!qdev_realize(DEVICE(obj), NULL, &error_fatal)) {
      error_report("ERROR: Failed to realize A72 CPU %d\n", i);
    }
  }

  qdev_realize(DEVICE(&s->a72_cluster), NULL, &error_fatal);
}

static void tda4vh_create_gic(TDA4VHSOCState *s, Error **errp) {
  QList *redist_region_count;
  redist_region_count = qlist_new();
  qlist_append_int(redist_region_count, TDA4VH_NUM_A72_CPUS);

  qemu_log("DEBUG: Starting GICv3 creation\n");
  qemu_log("DEBUG: Number of CPUs: %d\n", TDA4VH_NUM_A72_CPUS);
  qemu_log("DEBUG: Number of IRQs: %d\n", TDA4VH_GIC_SPI_INTR_NUM + 32);
  qemu_log("DEBUG: GIC Distributor base: 0x%08x\n", TDA4VH_GIC_APU_DISTRIBUTION_BASE);
  qemu_log("DEBUG: GIC Redistributor base: 0x%08x\n", TDA4VH_GIC_APU_REDISTRIBUTOR_0_BASE);

  /* Initialize the GIC device */
  object_initialize_child(OBJECT(s), "gic", &s->gic, TYPE_ARM_GICV3);

  /* Set the GIC properties */
  qdev_prop_set_uint32(DEVICE(&s->gic), "num-cpu", TDA4VH_NUM_A72_CPUS);
  qdev_prop_set_uint32(DEVICE(&s->gic), "num-irq", TDA4VH_GIC_SPI_INTR_NUM + 32);
  qdev_prop_set_uint32(DEVICE(&s->gic), "revision", 3);
  qdev_prop_set_array(DEVICE(&s->gic), "redist-region-count", redist_region_count);

  qemu_log("DEBUG: GIC properties set, realizing device...\n");
  /* Realize the GIC device */
  sysbus_realize(SYS_BUS_DEVICE(&s->gic), &error_abort);
  qemu_log("DEBUG: GIC device realized successfully\n");

  /* Map the GIC's MMIO regions */
  qemu_log("DEBUG: Mapping GIC MMIO regions...\n");
  sysbus_mmio_map(SYS_BUS_DEVICE(&s->gic), 0, TDA4VH_GIC_APU_DISTRIBUTION_BASE);
  qemu_log("DEBUG: Mapped GIC Distributor at 0x%08x\n", TDA4VH_GIC_APU_DISTRIBUTION_BASE);

  for(int i = 0;i < TDA4VH_NUM_A72_CPUS;i++)
  {
    uint32_t redist_addr = TDA4VH_GIC_APU_REDISTRIBUTOR_0_BASE + i * TDA4VH_GIC_APU_REDISTRIBUTOR_0_SIZE;
    sysbus_mmio_map(SYS_BUS_DEVICE(&s->gic), 1 + i * 2, redist_addr);
    qemu_log("DEBUG: Mapped GIC Redistributor %d at 0x%08x\n", i, redist_addr);
  }

  /* Connect CPU IRQ lines to the GIC*/
  qemu_log("DEBUG: Connecting CPU IRQ/FIQ lines to GIC...\n");
  for(int i = 0;i < TDA4VH_NUM_A72_CPUS;i++)
  {
    DeviceState *cpu_irq_in = DEVICE(&s->a72_cpus[i]);
    qemu_log("DEBUG: Connecting CPU%d IRQ line...\n", i);
    sysbus_connect_irq(SYS_BUS_DEVICE(&s->gic), i,
                       qdev_get_gpio_in(cpu_irq_in, ARM_CPU_IRQ));
    qemu_log("DEBUG: Connecting CPU%d FIQ line...\n", i);
    sysbus_connect_irq(SYS_BUS_DEVICE(&s->gic), i + TDA4VH_NUM_A72_CPUS,
                       qdev_get_gpio_in(cpu_irq_in, ARM_CPU_FIQ));
  }

  qemu_log("DEBUG: GICv3 creation and CPU connections completed\n");
}

static void tda4vh_create_uarts(TDA4VHSOCState *s, Error **errp){
  /* UART IRQ numbers corresponding to each UART */
  const int uart_irqs[] = {
    J784S4_UART0_IRQ, J784S4_UART1_IRQ, J784S4_UART2_IRQ, J784S4_UART3_IRQ,
    J784S4_UART4_IRQ, J784S4_UART5_IRQ, J784S4_UART6_IRQ, J784S4_UART7_IRQ,
    J784S4_UART8_IRQ, J784S4_UART9_IRQ
  };

  qemu_log("DEBUG: Starting UART creation and interrupt connection\n");
  qemu_log("DEBUG: GIC device pointer: %p\n", DEVICE(&s->gic));

  for (int i = 0; i < ARRAY_SIZE(s->uart); i++) {
    g_autofree char *uart_name = g_strdup_printf("uart%d", i);

    qemu_log("DEBUG: Creating UART%d...\n", i);
    object_initialize_child(OBJECT(s), uart_name, &s->uart[i], TYPE_PL011);
    qdev_prop_set_chr(DEVICE(&s->uart[i]), "chardev", serial_hd(i));
    sysbus_realize(SYS_BUS_DEVICE(&s->uart[i]), &error_abort);
    sysbus_mmio_map(SYS_BUS_DEVICE(&s->uart[i]), 0, j784s4_uart_addrs[i]);

    qemu_log("DEBUG: UART%d device pointer: %p\n", i, DEVICE(&s->uart[i]));
    qemu_log("DEBUG: UART%d sysbus device: %p\n", i, SYS_BUS_DEVICE(&s->uart[i]));

    /* Connect UART interrupt to GICv3 */
    qemu_log("DEBUG: Connecting UART%d IRQ %d to GIC...\n", i, uart_irqs[i]);
    sysbus_connect_irq(SYS_BUS_DEVICE(&s->uart[i]), 0,
                       qdev_get_gpio_in(DEVICE(&s->gic), uart_irqs[i]));

    /* Verify the connection was made */
    qemu_log("DEBUG: UART%d interrupt connection completed\n", i);
    qemu_log("INFO: Connected UART%d at 0x%08lx to IRQ %d\n",
             i, j784s4_uart_addrs[i], uart_irqs[i]);
  }

  qemu_log("DEBUG: All UART interrupt connections completed\n");
}

static void tda4vh_soc_realize(DeviceState *dev_soc, Error **errp) {
  MachineState *ms = MACHINE(qdev_get_machine());
  TDA4VHSOCState *s = TDA4VH_SOC(dev_soc);
  Error *err = NULL;

  tda4vh_create_a72_cpus(ms, s, &err);
  if (err) {
    error_report("ERROR: Error creating A72 CPUs: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  tda4vh_create_r5f_cpus(dev_soc, ms, &err);
  if (err) {
    error_report("ERROR: Error creating R5F CPUs: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  tda4vh_create_gic(s, &err);
  if (err)
  {
    error_report("ERROR: Error creating GICv3: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  tda4vh_create_uarts(s, &err);
  if (err)
  {
    error_report("ERROR: Error creating UART: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  /* Initialize TDA4VH's MailBox*/
  for (int i = 0; i < TDA4VH_MAILBOX_CLUSTER; i++) {
    g_autofree char *mailbox0_name = g_strdup_printf("mailbox0_%d", i);
    object_initialize_child(OBJECT(s), mailbox0_name, &s->MailBox0[i],
                            TYPE_TDA4_MAILBOX);
    sysbus_realize(SYS_BUS_DEVICE(&s->MailBox0[i]), &error_abort);
    if (error_abort) {
      error_report("ERROR: Failed to realize MailBox0 %d\n", i);
    }
    sysbus_mmio_map_overlap(SYS_BUS_DEVICE(&s->MailBox0[i]), 0,
                            TDA4VH_MAILBOX0_BASE + TDA4VH_MAILBOX_SIZE * i, 1);
  }

  for (int i = 0; i < TDA4VH_MAILBOX_CLUSTER; i++) {
    g_autofree char *mailbox1_name = g_strdup_printf("mailbox1_%d", i);
    object_initialize_child(OBJECT(s), mailbox1_name, &s->MailBox1[i],
                            TYPE_TDA4_MAILBOX);
    sysbus_realize(SYS_BUS_DEVICE(&s->MailBox1[i]), &error_abort);
    if (error_abort) {
      error_report("ERROR: Failed to realize MailBox1 %d\n", i);
    }
    sysbus_mmio_map_overlap(SYS_BUS_DEVICE(&s->MailBox1[i]), 0,
                            TDA4VH_MAILBOX1_BASE + TDA4VH_MAILBOX_SIZE * i, 1);
  }

  /* Create stub memory for unimplemented device*/
  memory_region_init_ram(&s->stubMemory, OBJECT(dev_soc), "stubMemory",
                         TDA4VH_STUB_MEMORY_SIZE, &err);
  memory_region_add_subregion(get_system_memory(), TDA4VH_STUB_MEMORY_BASE,
                              &s->stubMemory);
  
  /* Create DDR memory*/
  memory_region_init_ram(&s->ddr_ram, OBJECT(dev_soc), "ddr-ram",
                         TDA4VH_DDR0_MIN_SIZE, &err);
  if (err) {
    error_report("ERROR: Failed to init RAM: %s\n", error_get_pretty(err));
    error_propagate(errp, err);
    return;
  }
  memory_region_add_subregion(get_system_memory(), TDA4VH_DDR0_LINUX_START,
                              &s->ddr_ram);
}

static void tda4vh_soc_class_init(ObjectClass *oc, void *data) {
  DeviceClass *dc = DEVICE_CLASS(oc);
  dc->realize = tda4vh_soc_realize;
  dc->desc = "TDA4VH SoC";
  /* Reason: Uses serial_hds in realize function, thus can't be used twice */
  dc->user_creatable = false;
}

static const TypeInfo tda4vh_soc_info = {
    .name = TYPE_TDA4VH_SOC,
    .parent = TYPE_DEVICE,
    .instance_size = sizeof(TDA4VHSOCState),
    .class_init = tda4vh_soc_class_init,
};

static void tda4vh_soc_register_types(void) {
  type_register_static(&tda4vh_soc_info);
}

type_init(tda4vh_soc_register_types)
